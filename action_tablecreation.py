import mysql.connector

def create_action_table_scores(DB_CONFIG):

    # SQL query to create the action_table_scores
    create_table_query = '''
    CREATE TABLE IF NOT EXISTS action_table_scores (
        id INT AUTO_INCREMENT PRIMARY KEY,
        company_name VARCHAR(255),
        survey_url VARCHAR(1000) UNIQUE,

        communication_score FLOAT,
        communication_positive_pct FLOAT,
        communication_neutral_pct FLOAT,
        communication_negative_pct FLOAT,

        leadership_score FLOAT,
        leadership_positive_pct FLOAT,
        leadership_neutral_pct FLOAT,
        leadership_negative_pct FLOAT,

        worklife_balance_score FLOAT,
        worklife_balance_positive_pct FLOAT,
        worklife_balance_neutral_pct FLOAT,
        worklife_balance_negative_pct FLOAT,

        career_development_score FLOAT,
        career_development_positive_pct FLOAT,
        career_development_neutral_pct FLOAT,
        career_development_negative_pct FLOAT,

        recognition_rewards_score FLOAT,
        recognition_rewards_positive_pct FLOAT,
        recognition_rewards_neutral_pct FLOAT,
        recognition_rewards_negative_pct FLOAT,

        employee_engagement_score FLOAT,
        employee_engagement_positive_pct FLOAT,
        employee_engagement_neutral_pct FLOAT,
        employee_engagement_negative_pct FLOAT,

        workplace_environment_score FLOAT,
        workplace_environment_positive_pct FLOAT,
        workplace_environment_neutral_pct FLOAT,
        workplace_environment_negative_pct FLOAT,

        inclusion_diversity_score FLOAT,
        inclusion_diversity_positive_pct FLOAT,
        inclusion_diversity_neutral_pct FLOAT,
        inclusion_diversity_negative_pct FLOAT,

        compensation_transparency_score FLOAT,
        compensation_transparency_positive_pct FLOAT,
        compensation_transparency_neutral_pct FLOAT,
        compensation_transparency_negative_pct FLOAT,

        feedback_mechanisms_score FLOAT,
        feedback_mechanisms_positive_pct FLOAT,
        feedback_mechanisms_neutral_pct FLOAT,
        feedback_mechanisms_negative_pct FLOAT,

        organizational_transparency_score FLOAT,
        organizational_transparency_positive_pct FLOAT,
        organizational_transparency_neutral_pct FLOAT,
        organizational_transparency_negative_pct FLOAT,

        manager_employee_relationship_score FLOAT,
        manager_employee_relationship_positive_pct FLOAT,
        manager_employee_relationship_neutral_pct FLOAT,
        manager_employee_relationship_negative_pct FLOAT,

        psychological_safety_score FLOAT,
        psychological_safety_positive_pct FLOAT,
        psychological_safety_neutral_pct FLOAT,
        psychological_safety_negative_pct FLOAT,

        mission_values_alignment_score FLOAT,
        mission_values_alignment_positive_pct FLOAT,
        mission_values_alignment_neutral_pct FLOAT,
        mission_values_alignment_negative_pct FLOAT,

        innovation_creativity_score FLOAT,
        innovation_creativity_positive_pct FLOAT,
        innovation_creativity_neutral_pct FLOAT,
        innovation_creativity_negative_pct FLOAT,

        action_recommended TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    '''

    try:
        # Connect and execute
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute(create_table_query)
        print("✅ action_table_scores table created successfully.")

    except mysql.connector.Error as err:
        print(f"❌ MySQL Error: {err}")

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# Main entry
if __name__ == "__main__":
    create_action_table_scores()
