import torch
from transformers import <PERSON><PERSON>oken<PERSON>, RobertaForSequenceClassification


model_path = 'fine_tuned_roberta.pt'

# Load tokenizer
tokenizer = tokenizer = RobertaTokenizer.from_pretrained("roberta-base")

# Load model
model = RobertaForSequenceClassification.from_pretrained("roberta-base", num_labels=3)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model.load_state_dict(torch.load(model_path, map_location=device))
model.eval()

def predict_sentiment(text):
    inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True, max_length=128)
    with torch.no_grad():
        outputs = model(**inputs)
        prediction = torch.argmax(outputs.logits, dim=-1).item()
    return ["Negative", "Neutral", "Positive"][prediction]