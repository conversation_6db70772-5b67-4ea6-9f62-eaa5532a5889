import re

def refine_sentence(question_text, selected_option):
    # Clean question text
    question_text = re.sub(r"^\d+\.\s*", "", question_text)
    question_text = re.sub(r"\b(Do|Does|Is|Are|Can|How|What|Why|When|Where)\b", "", question_text, flags=re.IGNORECASE)
    question_text = re.sub(r"[^\w\s]", "", question_text)  # Remove punctuation
    question_text = question_text.strip().capitalize()

    # Clean selected option
    selected_option = re.sub(r"^\(?\)?\.?\s*", "", selected_option, flags=re.IGNORECASE)
    selected_option = re.sub(r"[^\w\s]", "", selected_option)  # Remove punctuation
    selected_option = selected_option.strip()

    return f"{question_text}, {selected_option}."