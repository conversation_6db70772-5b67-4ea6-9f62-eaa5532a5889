<!DOCTYPE html>
<html>
<head>
    <title>SURVEY PAGE</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

<style>
    :root {
        --primary: #2f68c0;
        --primary-dark: #1c4a94;
        --secondary: #9333ea;
        --accent: #0ea5e9;
        --success: #22c55e;
        --danger: #ef4444;
        --warning: #f59e0b;
        --light: #f8fafc;
        --dark: #1e293b;
        --gray: #94a3b8;
    }

    /* ===== Global Styles ===== */
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        background-color: var(--light);
        color: var(--dark);
        margin-top: 0px !important;
        position: static !important;
    }
    
    h1, h2 {
        color: var(--primary-dark);
        text-align: center;
    }
    
    /* ===== Navigation ===== */
    .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: black;
        padding: 10px 20px;
        position: sticky;
        top: 0;
        z-index: 1000;
        border-bottom: 2px solid var(--gray);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .nav-left {
        display: flex;
        align-items: center;
    }

    .nav-left .logo {
        width: auto;
        height: 50px;
        cursor: pointer;
        transition: transform 0.3s ease;
    }

    #google_translate_element {
        display: none !important;
        margin-left: 30px;
        margin-top: 0px; 
    }

    div[jsname="W297wb"] {
        display: none !important;
    }

    .goog-te-gadget {
        display: none !important;
    }

    .dropdown {
        position: relative;
        display: inline-block;
        margin-top: 10px;
        margin-right: 20px;
    }

    .goog-logo-link {
        display: none !important;
    }

    .goog-te-gadget {
        color: transparent !important;
    }

    .goog-te-gadget .goog-te-combo {
        color: var(--dark) !important;
        margin: 0 !important;
    }

    .goog-te-gadget span {
        display: none !important;
    }

    .VIpgJd-ZVi9od-l4eHX-hSRGPd,
    .VIpgJd-ZVi9od-ORHb-OEVmcd {
        display: none !important;
    }

    .nav-left .logo:hover {
        transform: rotate(15deg) scale(1.1);
    }

    .nav-right {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    /* ===== Language Selector ===== */
    .language-dropdown {
        position: relative;
    }

    .dropdown-btn {
        padding: 8px 12px;
        cursor: pointer;
        border: 1px solid var(--gray);
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: white;
        border-radius: 5px;
        font-size: 12px;
        width: 130px;
        color: var(--dark);
    }

    .dropdown-btn img {
        width: 20px;
        height: 15px;
        margin-right: 10px;
        border: 1px solid #eee;
    }

    .dropdown-content {
        display: none;
        position: absolute;
        background-color: white;
        border: 1px solid var(--gray);
        width: 100%;
        font-size:13px;
        z-index: 1000;
        border-radius: 5px;
        max-height: 200px;
        overflow-y: auto;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .dropdown-item {
        padding: 10px;
        display: flex;
        align-items: center;
        cursor: pointer;
        color: var(--dark);
        border-bottom: 1px solid #eee;
    }

    .dropdown-item:last-child {
        border-bottom: none;
    }

    .dropdown-item img {
        width: 20px;
        height: 15px;
        margin-right: 10px;
        border: 1px solid #eee;
    }

    .dropdown-item:hover {
        background-color: var(--light);
    }
    
    /* ===== Google Translate ===== */
    #google_translate_element {
        display: none !important;
    }
    h4{
        margin-left: 28%;
        font-size: 18px;
    }

    /* Hide Google Translate banner */
    .goog-te-banner-frame.skiptranslate,
    .goog-te-banner-frame,
    .goog-logo-link,
    #goog-gt-tt,
    .goog-te-balloon-frame,
    .goog-te-menu-value,
    .goog-te-gadget-icon {
        display: none !important;
        visibility: hidden !important;
        height: 0 !important;
    }

    .skiptranslate {
        display: none !important;
    }

    /* ===== Form Styles ===== */
    .container {
        background-color: white;
        padding: 60px;
        border-color: var(--gray);
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-top: 20px;
        border: 1px solid var(--gray);
        width: 1100px;
    }

    .container h1 {
        font-size: 40px;
        font-weight: bolder;
        text-transform: uppercase;
        color: var(--primary);
    }

    .container h2 {
        font-size: 30px;
        font-weight: bolder;
        color: var(--primary-dark);
    }
    
    .container h3 {
        font-size: 25px;
        text-align: center;
        color: var(--dark);
    }
    
    .form-row {
        display: flex;
        margin-bottom: 20px;
        gap: 20px;
        justify-content: center
    }

    form p {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 25px;
        font-weight: bold;
        color: var(--dark);
    }
    
    .form-group {
        max-width: 300px;
        flex: 1;
        margin-top: 25px;
    }

    .option-box {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
        font-size: 25px;
    }
    
    label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: var(--dark);
    }
    
    select, input[type="text"] {
        width: 100%;
        padding: 10px;
        border: 1px solid var(--gray);
        border-radius: 4px;
        box-sizing: border-box;
        font-size: 16px;
        background-color: white;
        color: var(--dark);
    }
    
    /* Consistent submit button style for both containers */
    .submit-btn {
        background-color: var(--primary);
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 20px;
        transition: background-color 0.3s ease;
    }
    
    .submit-btn:hover {
        background-color: var(--primary-dark);
    }
    
    /* Container 1 submit button positioning */
    .container .submit-btn2 {
        margin-left: auto;
        display: block;
        margin-top: 15px;
    }
    
    .other-field {
        display: none;
        margin-top: -4px;
    }
    
    h5 {
        margin-top: 90px;
        color: var(--gray);
        font-size: 15px;
        text-align: start;
    }

    /* =============container 2============ */
    .container2 h2,
    .container2 h4 {
        text-align: center;
    }

    .container2 form p {
        font-weight: bold;
        font-size: 23px;
    }

    .container2 input[type="radio"] {
        margin-right: 10px;
    }

    .option-box {
        border: 1px solid var(--gray);
        border-radius: 6px;
        width: 75%;
        padding: 10px;
        margin: 10px 0;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.2s ease;
        background-color: white;
    }

    .option-box:hover {
        background-color: var(--accent);
        color: white;
    }

    .option-box input[type="radio"] {
        margin-right: 12px;
    }

    /* Container 1 submit button positioning */
    .container .submit-btn {
        margin-left: auto;
        display: block;
        margin-top: 20px;
    }
    
    .submit-btn2 {
        background-color: var(--primary);
        color: white;
        padding: 10px 20px;
        border: none;
        margin-right: 25%;
        border-radius: 8px;
        cursor: pointer;
        font-size: 20px;
        transition: background-color 0.3s ease;
    }
    
    .submit-btn2:hover {
        background-color: var(--primary-dark);
    }

    .button-row {
        margin-top: 30px;
    }

    .button-row button {
        padding: 10px 20px;
        height: 50px;
        font-size: 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        background-color: var(--primary);
        color: white;
        transition: background-color 0.3s ease;
    }

    .button-row button:hover {
        background-color: var(--primary-dark);
    }
    
    .button-row2 {
        margin-top: -6%;
    }

    .button-row2 button {
        padding: 10px 20px;
        height: 30%;
        font-size: 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        background-color: var(--primary);
        color: white;
        transition: background-color 0.3s ease;
    }

    .button-row2 button:hover {
        background-color: var(--primary-dark);
    }
    
    hr {
        border: 0;
        height: 2px;
        background-color: var(--gray);
        margin: 10px 0;
    }

    /* ===== Progress Bar Styles ===== */
    .progress-container {
        width: 100%;
        height: 10px;
        background-color: #e0e0e0;
        border-radius: 5px;
        margin: 20px auto;
        overflow: hidden;
    }

    .progress-bar {
        height: 100%;
        background-color: var(--accent);
        transition: width 0.3s ease;
    }

    .progress-text {
        text-align: center;
        font-size: 14px;
        color: var(--gray);
        margin-bottom: 20px;
    }

    #sec {
        margin-left: 30px;
    }

   /* ===== Media Queries ===== */
    /* Large screens (1400px and up) - Mostly adjusting max-widths */
    @media (min-width: 1400px) {
        .container {
            width: 90%;
            max-width: 2000px;
            margin: 20px auto;
        } 
        
        .form-row {
            gap: 30px;
        }
        
        .form-group {
            max-width: 350px;
            font-size: 20px;
        }

        select, input[type="text"] {

     font-size: 17px;

        }
        
        .option-box {
            width: 70%;
            
            font-size: 20px;
        }
    }
         .container h1 {
            font-size: 33px;
        }
        
        .container h2 {
            font-size: 29px;
        }
        
        .container h3 {
            font-size: 25px;
        }
        
        form p {
            font-size: 25px;
        }

        
        
        
        .submit-btn, .submit-btn2 {
            font-size: 20px;
            
            padding: 8px 16px;
        }

        .container .submit-btn2{
            margin-top: 3%;
        }
        
        #sec {
            margin-left: 15px;
        }
        
        h4 {
            margin-left: 15%;
            font-size: 16px;
        }
    
        
    /* Medium screens (1024px) */
    @media (max-width: 1024px) {
        .container {
            width: 90%;
            padding: 40px;
        }
        
        .container h1 {
            font-size: 32px;
        }
        
        .container h2 {
            font-size: 26px;
        }
        
        .container h3 {
            font-size: 22px;
        }
        
        form p {
            font-size: 22px;
        }
        
        .option-box {
            width: 85%;
            font-size: 22px;
        }
        
        .form-row {
            flex-wrap: wrap;
        }
        
        .form-group {
            max-width: 100%;
            flex: 0 0 45%;
        }
        
        .button-row, .button-row2 {
            margin-top: 20px;
        }
        .container .submit-btn2{
            margin-top: -8%;
        }
        
    }

    /* Tablets (768px) */
    @media (max-width: 768px) {
        .navbar {
            padding: 10px 15px;
        }
        
        .nav-left .logo {
            height: 40px;
        }
        
        .dropdown-btn {
            width: 110px;
            font-size: 11px;
        }
        
        .container {
            width: 95%;
            padding: 30px;
        }
        
        .container h1 {
            font-size: 28px;
        }
        
        .container h2 {
            font-size: 24px;
        }
        
        .container h3 {
            font-size: 20px;
        }
        
        form p {
            font-size: 20px;
        }
        
        .option-box {
            width: 90%;
            font-size: 20px;
            padding: 8px;
        }
        
        .form-row {
            flex-direction: column;
            gap: 15px;
        }
        
        .form-group {
            max-width: 100%;
            flex: 1;
        }
        
        .submit-btn, .submit-btn2 {
            font-size: 18px;
            padding: 8px 16px;
        }
        
        #sec {
            margin-left: 15px;
        }
        
        h4 {
            margin-left: 15%;
            font-size: 16px;
        }
        .container .submit-btn2{
            margin-top: -9.5%;
            height: 50px;
        }
    }

    /* Mobile (480px) - Modified to keep nav in row */
    @media (max-width: 480px) {
        .navbar {
            flex-direction: row;
            padding: 8px 10px;
        }
        
        .nav-left, .nav-right {
            width: auto;
            justify-content: flex-start;
            margin-bottom: 0;
        }
        
        .nav-left .logo {
            height: 35px;
        }
        
        .dropdown-btn {
            width: 100px;
            padding: 6px 8px;
            font-size: 10px;
        }
        
        .dropdown-btn img {
            width: 16px;
            height: 12px;
            margin-right: 5px;
        }
        
        .container {
            width: 90%;
            padding: 15px 10px;
            border-radius: 10;
        }
        
        .container h1 {
            font-size: 22px;
        }
        
        .container h2 {
            font-size: 18px;
        }
        
        .container h3 {
            font-size: 16px;
        }
        
        form p {
            font-size: 16px;
        }
        
        .option-box {
            width: 100%;
            font-size: 16px;
            padding: 6px;
        }
        
        .form-row {
            margin-bottom: 8px;
        }
        
        label {
            font-size: 14px;
            margin-bottom: 4px;
        }
        
        select, input[type="text"] {
            padding: 7px;
            font-size: 14px;
        }
        
        .submit-btn, .submit-btn2 {
            font-size: 15px;
            width: 100%;
            margin-top: 12px;
        }
        
        .button-row button {
            width: 30%;
            margin-right: 100px;
            margin-bottom: 8px;
        }

       .container .submit-btn2{
        width: 30%;
            margin-top: -30%;
            margin-left: 155px;
            height: 50px;
        }
        
        #sec {
            margin-left: 0;
        }
        
        h4 {
            margin-left: 5%;
            font-size: 13px;
        }
        
        h5 {
            font-size: 11px;
            margin-top: 25px;
        }
        
        .progress-text {
            font-size: 11px;
        }

        
    }
</style>
</head>
<body>
   <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-left">
            
                <img src="https://frandzzo-web-content.s3.amazonaws.com/images/logo-white.png" alt="Logo" class="logo">
            </a>
        </div>
        <div class="nav-right">
            <!-- Language Selector -->
            <div class="language-dropdown">
                <div id="google_translate_element"></div>
                <div class="dropdown">
                    <div class="dropdown-btn" id="dropdownBtn">
                        <img id="selected-flag" src="https://flagcdn.com/w320/us.png" alt="USA Flag" />
                        <span id="selected-language" class="notranslate">English</span>
                    </div>
                    <div class="dropdown-content" id="dropdownContent">
                        <div class="dropdown-item notranslate" data-value="en" data-text="English" data-flag="https://flagcdn.com/w320/us.png">
                            <img src="https://flagcdn.com/w320/us.png" alt="USA Flag" /> English
                        </div>
                          <div class="dropdown-item notranslate" data-value="en" data-text="English" data-flag="https://flagcdn.com/w320/us.png">
                            <img src="https://flagcdn.com/w320/in.png" alt="USA Flag" /> English
                        </div>
                           
                      
                         <div class="dropdown-item notranslate" data-value="ja" data-text="Japanese" data-flag="https://flagcdn.com/w320/jp.png">
                            <img src="https://flagcdn.com/w320/jp.png" alt="Japan Flag" /> Japanese
                        </div>
                        <div class="dropdown-item notranslate" data-value="de" data-text="German" data-flag="https://flagcdn.com/w320/de.png">
                            <img src="https://flagcdn.com/w320/de.png" alt="Germany Flag" /> German
                        </div>
                        <div class="dropdown-item notranslate" data-value="zh-TW" data-text="Chinese" data-flag="https://flagcdn.com/w320/cn.png">
                            <img src="https://flagcdn.com/w320/cn.png" alt="China Flag" /> Chinese
                        </div>
                        <div class="dropdown-item notranslate" data-value="sv" data-text="Swedish" data-flag="https://flagcdn.com/w320/se.png">
                            <img src="https://flagcdn.com/w320/se.png" alt="Sweden Flag" /> Swedish
                        </div>
                           <div class="dropdown-item notranslate" data-value="nl" data-text="Dutch" data-flag="https://flagcdn.com/w320/nl.png">
                            <img src="https://flagcdn.com/w320/nl.png" alt="Netherlands Flag" /> Dutch
                        </div>
                        <div class="dropdown-item notranslate" data-value="ar" data-text="Arabic" data-flag="https://flagcdn.com/w320/ae.png">
                            <img src="https://flagcdn.com/w320/ae.png" alt="UAE Flag" /> Arabic
                        </div>
                        <div class="dropdown-item notranslate" data-value="it" data-text="Italian" data-flag="https://flagcdn.com/w320/it.png">
                            <img src="https://flagcdn.com/w320/it.png" alt="Italy Flag" /> Italian
                        </div><div class="dropdown-item notranslate" data-value="ru" data-text="Russian" data-flag="https://flagcdn.com/w320/ru.png">
                            <img src="https://flagcdn.com/w320/ru.png" alt="Russia Flag" /> Russian
                        </div>
                        <div class="dropdown-item notranslate" data-value="ko" data-text="Korean" data-flag="https://flagcdn.com/w320/kr.png">
                            <img src="https://flagcdn.com/w320/kr.png" alt="South Korea Flag" /> Korean
                        </div>
                        <div class="dropdown-item notranslate" data-value="ms" data-text="Malay" data-flag="https://flagcdn.com/w320/my.png">
                            <img src="https://flagcdn.com/w320/my.png" alt="Malaysia Flag" /> Malay
                        </div>
                        <div class="dropdown-item notranslate" data-value="th" data-text="Thai" data-flag="https://flagcdn.com/w320/th.png">
                            <img src="https://flagcdn.com/w320/th.png" alt="Thailand Flag" /> Thai
                        </div>
                      
                      
                    </div>
                   
                </div>
    </div>
</nav>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Survey</title>
    <style>
        /* Your existing CSS styles */
    </style>
</head>
<body>
    <div class="container">
        {% if company %}
            <h1>{{ company }}</h1>
        {% endif %}

        {% if stage == 'user' %}
        <h2>Employee Information</h2>
        <h3>Let's Start With Your Basic Details</h3>
        <hr>
        <div class="form-container">
            <form method="POST">
                
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="gender">Gender</label>
                        <select id="gender" name="gender" required>
                            <option value="">Select</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="age_group">Age Group</label>
                        <select id="age_group" name="age_group" required>
                            <option value="">Select</option>
                            <option value="18-25">18-25</option>
                            <option value="26-35">26-35</option>
                            <option value="36-45">36-45</option>
                            <option value="46+">46+</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="tenure_group">Tenure Group</label>
                        <select id="tenure_group" name="tenure_group" required>
                            <option value="">Select</option>
                            <option value="0-1 year">0-1 year</option>
                            <option value="1-3 years">1-3 years</option>
                            <option value="3-5 years">3-5 years</option>
                            <option value="5+ years">5+ years</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select id="role" name="role" required>
                            <option value="">Select</option>
                            <option value="Junior Staff">Junior Staff</option>
                            <option value="Senior Staff">Senior Staff</option>
                            <option value="Manager">Manager</option>
                            <option value="Executive">Executive</option>
                            <option value="Trainee">Trainee</option>
                            <option value="Team">Team Member</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="department">Department</label>
                        <select id="department" name="department" required>
                            <option value="">Select</option>
                            <option value="HR">Human Resources & Admin</option>
                            <option value="Finance">Finance and Accounting</option>
                            <option value="Sales">Sales and Marketing</option>
                            <option value="Product">Product Development</option>
                            <option value="Technical">Technical</option>
                            <option value="Operations">Operations</option>
                            <option value="Procurements">Procurement</option>
                            <option value="Quality">Quality</option>
                            <option value="Business">Business Development</option>
                            <option value="Executive">Executive</option>
                            <option value="Leadership">Leadership</option>
                            <option value="Management">Management</option>
                            <option value="Others">Others (Please Specify)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <div id="otherDepartmentField" class="other-field">
                            <label for="other_department">Other</label>
                            <input type="text" id="other_department" name="other_department">
                        </div>
                    </div>
                </div>

                <input type="submit" class="submit-btn" value="Next">
            </form>
        </div>
        {% elif stage == 'question' %}
        <div class="container2">
            <h2>Amazing Place To Work</h2>
            <h3>Your Voice . Our Culture . A Better Tomorrow</h3>
            <hr>
            <div id="sec">
                <div class="progress-container">
                    <div class="progress-bar" style="width: {{ (qnum / QUESTIONS|length) * 100 }}%"></div>
                </div>
                <div class="progress-text">{{ qnum }} of {{ QUESTIONS|length }} questions</div>

                {% if error %}
                <div class="error-message">{{ error }}</div>
                {% endif %}

                <form method="POST">
                    <p>{{ qnum }}. {{ question.q }}</p>
                    {% for opt in question.options %}
                    <label class="option-box">
                        <input type="radio" name="option" value="{{ opt }}" 
                               {% if selected_answer == opt %}checked{% endif %} required>
                        {{ opt }}
                    </label>
                    {% endfor %}
                    <br>
                    <div class="button-row">
                        {% if qnum > 1 %}
                        <button type="submit" name="back" value="true" formnovalidate>Back</button>
                        {% endif %}
                    </div>
                    <div class="button-row2">
                        <input type="submit" class="submit-btn2" value="Next">
                    </div>
                    <h5>Powered By Frandzzo Global - Next Gen AI Tech</h5>
                </form>
            </div>
        </div>
        {% endif %}
    </div>

    <script>
        // Show other department field when "Others" is selected
        document.getElementById('department').addEventListener('change', function() {
            const otherField = document.getElementById('otherDepartmentField');
            if (this.value === 'Others') {
                otherField.style.display = 'block';
            } else {
                otherField.style.display = 'none';
            }
        });
    </script>

     <script>
    function googleTranslateElementInit() {
      new google.translate.TranslateElement(
        { pageLanguage: "en" },
        "google_translate_element"
      );
    }
    
    const dropdownBtn = document.getElementById("dropdownBtn");
    const dropdownContent = document.getElementById("dropdownContent");
    
    dropdownBtn.addEventListener("click", function () {
      dropdownContent.style.display =
        dropdownContent.style.display === "block" ? "none" : "block";
    });
    
    // Close dropdown when clicking outside
    document.addEventListener("click", function(event) {
      if (!dropdownBtn.contains(event.target) && !dropdownContent.contains(event.target)) {
        dropdownContent.style.display = "none";
      }
    });
    
    document.querySelectorAll(".dropdown-item").forEach((item) => {
      item.addEventListener("click", function () {
        const flagSrc = this.getAttribute("data-flag");
        const languageText = this.getAttribute("data-text");
        const lang = this.getAttribute("data-value");
      
        document.getElementById("selected-flag").src = flagSrc;
        document.getElementById("selected-language").textContent = languageText;
      
        dropdownContent.style.display = "none";
      
        const languageSelect = document.querySelector("select.goog-te-combo");
        if (languageSelect) {
          languageSelect.value = lang;
          languageSelect.dispatchEvent(new Event("change"));
        }
      });
    });
  </script>
  <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
</body>
</html>