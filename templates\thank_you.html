<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Thank You for Your Feedback</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <style>
   :root {
  --primary: #2f68c0;        
  --primary-dark: #1c4a94;  
  --secondary: #9333ea;     
  --accent: #0ea5e9;        
  --success: #22c55e;        
  --danger: #ef4444;        
  --warning: #f59e0b;        
  --light: #f8fafc;         
  --dark: #1e293b;           
  --gray: #94a3b8;           
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--light);
  color: var(--dark);
  line-height: 1.6;
  margin-top: 0px !important;
  position: static !important;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: black;
  padding: 12px 30px;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-left .logo {
  width: auto;
  height: 50px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.nav-left .logo:hover {
  transform: scale(1.05);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.language-dropdown {
  position: relative;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-btn {
  padding: 8px 15px;
  cursor: pointer;
  border: 1px solid var(--gray);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  border-radius: 8px;
  font-size: 13px;
  width: 140px;
  color: var(--dark);
  transition: all 0.3s ease;
}

.dropdown-btn:hover {
  border-color: var(--success);
}

.dropdown-btn img {
  width: 20px;
  height: 15px;
  margin-right: 10px;
  border: 1px solid #eee;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: white;
  border: 1px solid var(--gray);
  width: 100%;
  font-size: 13px;
  z-index: 1000;
  border-radius: 8px;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
  padding: 10px 15px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--dark);
  border-bottom: 1px solid #eee;
  transition: all 0.3s ease;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item img {
  width: 20px;
  height: 15px;
  margin-right: 10px;
  border: 1px solid #eee;
}

.dropdown-item:hover {
  background-color: var(--light);
  color: var(--success);
}

/* Google Translate Hidden */
#google_translate_element,
.goog-te-banner-frame.skiptranslate,
.goog-te-banner-frame,
.goog-logo-link,
#goog-gt-tt,
.goog-te-balloon-frame,
.goog-te-menu-value,
.goog-te-gadget-icon,
.skiptranslate {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
}

/* Main Content */
.thank-you-container {
  max-width: 800px;
  margin: 100px auto 50px;
  padding: 40px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.thank-you-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--success), var(--primary));
}

.thank-you-icon {
  font-size: 60px;
  color: var(--success);
  margin-bottom: 20px;
  animation: bounce 1s ease infinite alternate;
}

@keyframes bounce {
  from { transform: translateY(0); }
  to { transform: translateY(-10px); }
}

.content-section h1 {
  color: var(----dark);
  font-size: 2.2em;
  margin: 20px 0 15px;
  font-weight: 600;
}

.content-section p {
  font-size: 1em;
  color: var(--primary-dark);
  margin-bottom: 30px;
  line-height: 1.8;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .thank-you-container {
    margin: 80px 20px 40px;
    padding: 30px 20px;
  }

  .content-section h1 {
    font-size: 1.8em;
  }

  .content-section p {
    font-size: 1em;
  }

  .navbar {
    padding: 10px 15px;
  }

  .dropdown-btn {
    width: 120px;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .thank-you-container {
    margin: 60px 15px 30px;
  }

  .thank-you-icon {
    font-size: 50px;
  }

  .content-section h1 {
    font-size: 1.6em;
  }

  .dropdown-btn {
    width: 100px;
    font-size: 12px;
  }

  .dropdown-btn img {
    margin-right: 5px;
  }
}

@media (min-width: 2560px) {
  body {
    font-size: 18px;
    line-height: 1.8;
  }

  .thank-you-container {
    max-width: 1200px;
    margin: 150px auto 80px;
    padding: 60px;
  }

  .content-section h1 {
    font-size: 2.8em;
  }

  .content-section p {
    font-size: 2.2em;
    max-width: 1100px;
  }

  .thank-you-icon {
    font-size: 80px;
  }

  .navbar {
    padding: 20px 60px;
  }

  .dropdown-btn {
    font-size: 20px;
    width: 180px;
    padding: 10px 20px;
  }

  .dropdown-item {
    font-size: 14px;
    padding: 12px 20px;
  }

  .nav-left .logo {
    height: 60px;
  }

  .nav-right {
    gap: 30px;
  }
}

  </style>
</head>
<body>

  <!-- Navigation Bar -->
  <nav class="navbar">
    <div class="nav-left">
      
        <img src="https://frandzzo-web-content.s3.amazonaws.com/images/logo-white.png" alt="Logo" class="logo">
      </a>
    </div>
    <div class="nav-right">
      <!-- Language Selector -->
      <div class="language-dropdown">
        <div id="google_translate_element"></div>
        <div class="dropdown">
          <div class="dropdown-btn" id="dropdownBtn">
            <img id="selected-flag" src="https://flagcdn.com/w320/us.png" alt="USA Flag" />
            <span id="selected-language" class="notranslate">English</span>
          </div>
          <div class="dropdown-content" id="dropdownContent">
            <div class="dropdown-item notranslate" data-value="en" data-text="English" data-flag="https://flagcdn.com/w320/us.png">
              <img src="https://flagcdn.com/w320/us.png" alt="USA Flag" /> English
            </div>
            <div class="dropdown-item notranslate" data-value="en" data-text="English" data-flag="https://flagcdn.com/w320/in.png">
              <img src="https://flagcdn.com/w320/in.png" alt="India Flag" /> English (IN)
            </div>
            <div class="dropdown-item notranslate" data-value="ja" data-text="Japanese" data-flag="https://flagcdn.com/w320/jp.png">
              <img src="https://flagcdn.com/w320/jp.png" alt="Japan Flag" /> Japanese
            </div>
            <div class="dropdown-item notranslate" data-value="de" data-text="German" data-flag="https://flagcdn.com/w320/de.png">
              <img src="https://flagcdn.com/w320/de.png" alt="Germany Flag" /> German
            </div>
            <div class="dropdown-item notranslate" data-value="zh-TW" data-text="Chinese" data-flag="https://flagcdn.com/w320/cn.png">
              <img src="https://flagcdn.com/w320/cn.png" alt="China Flag" /> Chinese
            </div>
            <div class="dropdown-item notranslate" data-value="sv" data-text="Swedish" data-flag="https://flagcdn.com/w320/se.png">
              <img src="https://flagcdn.com/w320/se.png" alt="Sweden Flag" /> Swedish
            </div>
            <div class="dropdown-item notranslate" data-value="nl" data-text="Dutch" data-flag="https://flagcdn.com/w320/nl.png">
              <img src="https://flagcdn.com/w320/nl.png" alt="Netherlands Flag" /> Dutch
            </div>
            <div class="dropdown-item notranslate" data-value="ar" data-text="Arabic" data-flag="https://flagcdn.com/w320/ae.png">
              <img src="https://flagcdn.com/w320/ae.png" alt="UAE Flag" /> Arabic
            </div>
            <div class="dropdown-item notranslate" data-value="it" data-text="Italian" data-flag="https://flagcdn.com/w320/it.png">
              <img src="https://flagcdn.com/w320/it.png" alt="Italy Flag" /> Italian
            </div>
            <div class="dropdown-item notranslate" data-value="ru" data-text="Russian" data-flag="https://flagcdn.com/w320/ru.png">
              <img src="https://flagcdn.com/w320/ru.png" alt="Russia Flag" /> Russian
            </div>
            <div class="dropdown-item notranslate" data-value="ko" data-text="Korean" data-flag="https://flagcdn.com/w320/kr.png">
              <img src="https://flagcdn.com/w320/kr.png" alt="South Korea Flag" /> Korean
            </div>
            <div class="dropdown-item notranslate" data-value="ms" data-text="Malay" data-flag="https://flagcdn.com/w320/my.png">
              <img src="https://flagcdn.com/w320/my.png" alt="Malaysia Flag" /> Malay
            </div>
            <div class="dropdown-item notranslate" data-value="th" data-text="Thai" data-flag="https://flagcdn.com/w320/th.png">
              <img src="https://flagcdn.com/w320/th.png" alt="Thailand Flag" /> Thai
            </div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Thank You Container -->
  <div class="thank-you-container">
    <div class="thank-you-icon">
      <i class="fas fa-check-circle"></i>
    </div>
    
    <div class="content-section">
      <h1>"We Value Your Feedback"</h1>
      <p>Your voice plays a vital role in shaping a better workplace for all of us.</p>
    </div>


  <script>
    function googleTranslateElementInit() {
      new google.translate.TranslateElement(
        { pageLanguage: "en" },
        "google_translate_element"
      );
    }
    
    const dropdownBtn = document.getElementById("dropdownBtn");
    const dropdownContent = document.getElementById("dropdownContent");
    
    dropdownBtn.addEventListener("click", function () {
      dropdownContent.style.display =
        dropdownContent.style.display === "block" ? "none" : "block";
    });
    
    // Close dropdown when clicking outside
    document.addEventListener("click", function(event) {
      if (!dropdownBtn.contains(event.target) && !dropdownContent.contains(event.target)) {
        dropdownContent.style.display = "none";
      }
    });
    
    document.querySelectorAll(".dropdown-item").forEach((item) => {
      item.addEventListener("click", function () {
        const flagSrc = this.getAttribute("data-flag");
        const languageText = this.getAttribute("data-text");
        const lang = this.getAttribute("data-value");
      
        document.getElementById("selected-flag").src = flagSrc;
        document.getElementById("selected-language").textContent = languageText;
      
        dropdownContent.style.display = "none";
      
        const languageSelect = document.querySelector("select.goog-te-combo");
        if (languageSelect) {
          languageSelect.value = lang;
          languageSelect.dispatchEvent(new Event("change"));
        }
      });
    });
  </script>
  <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
</body>
</html>